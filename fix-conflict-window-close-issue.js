/**
 * 修复冲突解决窗口无法关闭的问题
 * 根据用户提供的图片，解决"解决冲突"窗口关闭按钮无效的问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 修复冲突解决窗口关闭问题...\n');

// 检查文件是否存在
function checkFileExists(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return false;
  }
  return true;
}

// 检查文件内容
function checkFileContent(filePath, pattern, description) {
  try {
    if (!checkFileExists(filePath)) {
      return false;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const hasPattern = pattern.test(content);
    
    if (hasPattern) {
      console.log(`✅ ${description}: 已修复`);
    } else {
      console.log(`❌ ${description}: 需要修复`);
    }
    
    return hasPattern;
  } catch (error) {
    console.log(`❌ 检查文件内容失败 ${filePath}: ${error.message}`);
    return false;
  }
}

console.log('1. 检查测试冲突自动创建是否已禁用...');

// 检查testFixes.ts是否已禁用自动创建冲突
checkFileContent(
  'editor/src/utils/testFixes.ts',
  /\/\/ setTimeout\(\(\) => \{[\s\S]*?\/\/   runAllTests\(\);[\s\S]*?\/\/ \}, 3000\);/,
  '自动创建测试冲突已禁用'
);

console.log('\n2. 检查ConflictResolutionDialog关闭功能...');

// 检查handleCancel函数优化
checkFileContent(
  'editor/src/components/collaboration/ConflictResolutionDialog.tsx',
  /const handleCancel = React\.useCallback\(/,
  'handleCancel使用useCallback优化'
);

// 检查关闭日志记录
checkFileContent(
  'editor/src/components/collaboration/ConflictResolutionDialog.tsx',
  /console\.log\('ConflictResolutionDialog: 关闭回调已调用'\)/,
  '关闭回调日志记录'
);

// 检查Modal配置
checkFileContent(
  'editor/src/components/collaboration/ConflictResolutionDialog.tsx',
  /closable={true}/,
  'Modal可关闭配置'
);

checkFileContent(
  'editor/src/components/collaboration/ConflictResolutionDialog.tsx',
  /keyboard={true}/,
  'Modal键盘关闭配置'
);

checkFileContent(
  'editor/src/components/collaboration/ConflictResolutionDialog.tsx',
  /maskClosable={true}/,
  'Modal遮罩关闭配置'
);

checkFileContent(
  'editor/src/components/collaboration/ConflictResolutionDialog.tsx',
  /destroyOnClose={true}/,
  'Modal销毁配置'
);

checkFileContent(
  'editor/src/components/collaboration/ConflictResolutionDialog.tsx',
  /zIndex=\{1000\}/,
  'Modal层级配置'
);

console.log('\n3. 检查ConflictPanel关闭功能...');

// 检查ConflictPanel关闭按钮
checkFileContent(
  'editor/src/components/collaboration/ConflictPanel.tsx',
  /onClick={handleClose}/,
  'ConflictPanel关闭按钮事件'
);

// 检查ConflictPanel关闭函数
checkFileContent(
  'editor/src/components/collaboration/ConflictPanel.tsx',
  /const handleClose = React\.useCallback\(/,
  'ConflictPanel handleClose使用useCallback'
);

console.log('\n4. 检查App.tsx全局冲突面板渲染...');

// 检查全局ConflictPanel渲染
checkFileContent(
  'editor/src/App.tsx',
  /\{showConflictPanel && \(/,
  '全局ConflictPanel条件渲染'
);

// 检查ConflictPanel导入
checkFileContent(
  'editor/src/App.tsx',
  /import ConflictPanel from '\.\/components\/collaboration\/ConflictPanel'/,
  'ConflictPanel组件导入'
);

console.log('\n5. 检查配置文件一致性...');

// 检查.env文件
if (checkFileExists('.env')) {
  checkFileContent(
    '.env',
    /REACT_APP_API_URL=http:\/\/localhost:3000\/api/,
    '.env API URL配置'
  );
}

// 检查docker-compose.windows.yml
if (checkFileExists('docker-compose.windows.yml')) {
  checkFileContent(
    'docker-compose.windows.yml',
    /REACT_APP_API_URL=\/api/,
    'docker-compose API URL配置'
  );
}

console.log('\n📊 修复建议:');
console.log('1. ✅ 已禁用自动创建测试冲突数据，避免冲突窗口自动弹出');
console.log('2. ✅ 优化了ConflictResolutionDialog的关闭功能');
console.log('3. ✅ 改进了Modal配置，支持多种关闭方式');
console.log('4. ✅ 确保了全局冲突面板的正确渲染');

console.log('\n🎯 用户操作指南:');
console.log('1. 重新启动前端服务以应用修复');
console.log('2. 如果仍有冲突窗口显示，可以通过以下方式关闭:');
console.log('   - 点击窗口右上角的X按钮');
console.log('   - 点击"取消"按钮');
console.log('   - 按ESC键');
console.log('   - 点击窗口外的遮罩区域');
console.log('3. 如需手动测试冲突功能，在浏览器控制台运行:');
console.log('   window.testConflictFunctions.createTestConflict()');

console.log('\n🚀 重启服务命令:');
console.log('Windows: ./start-windows.ps1');
console.log('或者单独重启前端: docker-compose -f docker-compose.windows.yml restart editor');

console.log('\n✅ 冲突解决窗口关闭问题修复完成！');
