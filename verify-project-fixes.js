#!/usr/bin/env node
/**
 * 项目修复验证脚本
 * 验证冲突解决窗口关闭功能和项目卡片路由跳转功能的修复
 */

const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

function checkFileContent(filePath, pattern, description) {
  if (!checkFileExists(filePath)) {
    colorLog('red', `❌ 文件不存在: ${filePath}`);
    return false;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const matches = pattern.test(content);
  
  if (matches) {
    colorLog('green', `✅ ${description}: ${filePath}`);
    return true;
  } else {
    colorLog('red', `❌ ${description}: ${filePath}`);
    return false;
  }
}

console.log('🔍 开始验证项目修复...\n');

let allTestsPassed = true;

// 1. 验证冲突解决窗口关闭功能修复
colorLog('cyan', '1. 验证冲突解决窗口关闭功能修复...');

// 检查App.tsx中的冲突面板状态选择器修复
if (!checkFileContent(
  'editor/src/App.tsx',
  /const showConflictPanel = useAppSelector\(selectShowConflictPanel\)/,
  'App.tsx中使用正确的冲突面板状态选择器'
)) {
  allTestsPassed = false;
}

// 检查ConflictPanel的关闭按钮
if (!checkFileContent(
  'editor/src/components/collaboration/ConflictPanel.tsx',
  /const handleClose = React\.useCallback\(/,
  'ConflictPanel关闭函数使用useCallback'
)) {
  allTestsPassed = false;
}

// 检查ConflictResolutionDialog的Modal配置
if (!checkFileContent(
  'editor/src/components/collaboration/ConflictResolutionDialog.tsx',
  /destroyOnClose={true}/,
  'ConflictResolutionDialog Modal destroyOnClose配置'
)) {
  allTestsPassed = false;
}

// 2. 验证项目卡片路由跳转功能修复
colorLog('cyan', '\n2. 验证项目卡片路由跳转功能修复...');

// 检查项目卡片的onClick事件
if (!checkFileContent(
  'editor/src/pages/ProjectsPage.tsx',
  /onClick=\{\(\) => handleOpenProject\(project\)\}/,
  'ProjectsPage项目卡片onClick事件'
)) {
  allTestsPassed = false;
}

// 检查按钮的stopPropagation
if (!checkFileContent(
  'editor/src/pages/ProjectsPage.tsx',
  /e\.stopPropagation\(\)/,
  'ProjectsPage按钮事件阻止冒泡'
)) {
  allTestsPassed = false;
}

// 检查handleOpenProject函数
if (!checkFileContent(
  'editor/src/pages/ProjectsPage.tsx',
  /const handleOpenProject = async \(project: any\) => \{/,
  'ProjectsPage handleOpenProject函数定义'
)) {
  allTestsPassed = false;
}

// 3. 验证路由配置
colorLog('cyan', '\n3. 验证路由配置...');

// 检查编辑器路由配置
if (!checkFileContent(
  'editor/src/App.tsx',
  /<Route path="editor\/:projectId\/:sceneId" element={<EditorPage \/>} \/>/,
  'App.tsx编辑器路由配置'
)) {
  allTestsPassed = false;
}

// 检查EditorPage参数获取
if (!checkFileContent(
  'editor/src/pages/EditorPage.tsx',
  /const \{ projectId, sceneId \} = useParams<\{ projectId: string; sceneId: string \}>\(\)/,
  'EditorPage路由参数获取'
)) {
  allTestsPassed = false;
}

// 4. 验证Modal组件关闭属性修复
colorLog('cyan', '\n4. 验证Modal组件关闭属性修复...');

// 检查MainLayout中的Modal配置
if (!checkFileContent(
  'editor/src/components/MainLayout.tsx',
  /destroyOnClose[\s\n]*keyboard=\{true\}[\s\n]*maskClosable=\{true\}[\s\n]*centered=\{true\}/,
  'MainLayout Modal属性配置'
)) {
  allTestsPassed = false;
}

// 检查ParticleEditor Modal配置
if (!checkFileContent(
  'editor/src/components/ParticleEditor/example.tsx',
  /destroyOnClose[\s\n]*keyboard=\{true\}[\s\n]*maskClosable=\{true\}[\s\n]*centered=\{true\}/,
  'ParticleEditor Modal属性配置'
)) {
  allTestsPassed = false;
}

// 5. 验证配置文件一致性
colorLog('cyan', '\n5. 验证配置文件一致性...');

// 检查.env文件
if (!checkFileExists('.env')) {
  colorLog('red', '❌ .env文件不存在');
  allTestsPassed = false;
} else {
  colorLog('green', '✅ .env文件存在');
}

// 检查docker-compose.windows.yml文件
if (!checkFileExists('docker-compose.windows.yml')) {
  colorLog('red', '❌ docker-compose.windows.yml文件不存在');
  allTestsPassed = false;
} else {
  colorLog('green', '✅ docker-compose.windows.yml文件存在');
}

// 检查启动脚本
if (!checkFileExists('start-windows.ps1')) {
  colorLog('red', '❌ start-windows.ps1文件不存在');
  allTestsPassed = false;
} else {
  colorLog('green', '✅ start-windows.ps1文件存在');
}

// 检查停止脚本
if (!checkFileExists('stop-windows.ps1')) {
  colorLog('red', '❌ stop-windows.ps1文件不存在');
  allTestsPassed = false;
} else {
  colorLog('green', '✅ stop-windows.ps1文件存在');
}

// 检查编辑器Dockerfile
if (!checkFileExists('editor/Dockerfile')) {
  colorLog('red', '❌ editor/Dockerfile文件不存在');
  allTestsPassed = false;
} else {
  colorLog('green', '✅ editor/Dockerfile文件存在');
}

// 输出最终结果
console.log('\n' + '='.repeat(60));
if (allTestsPassed) {
  colorLog('green', '🎉 所有验证测试通过！');
  colorLog('cyan', '\n📋 修复总结:');
  colorLog('green', '✅ 冲突解决窗口关闭功能已修复');
  colorLog('green', '✅ 项目卡片路由跳转功能已修复');
  colorLog('green', '✅ 路由配置验证通过');
  colorLog('green', '✅ Modal组件关闭属性已修复');
  colorLog('green', '✅ 配置文件一致性验证通过');
  
  colorLog('cyan', '\n🚀 启动建议:');
  colorLog('yellow', '1. 重启前端服务以应用修复:');
  colorLog('white', '   ./start-windows.ps1');
  colorLog('yellow', '2. 或者单独重启编辑器服务:');
  colorLog('white', '   docker-compose -f docker-compose.windows.yml restart editor');
  
  colorLog('cyan', '\n🎯 功能验证:');
  colorLog('white', '1. 访问 http://localhost 进入项目管理界面');
  colorLog('white', '2. 点击项目卡片验证路由跳转功能');
  colorLog('white', '3. 如果出现冲突窗口，验证关闭功能');
  
} else {
  colorLog('red', '❌ 部分验证测试失败，请检查上述错误信息');
}

console.log('\n' + '='.repeat(60));
