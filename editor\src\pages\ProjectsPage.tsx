/**
 * 项目页面
 */
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  List,
  Typography,
  Space,
  Input,
  Modal,
  Form,
  Select,
  Tabs,
  Tag,
  Tooltip,
  Empty,
  Spin,
  message } from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FolderOpenOutlined,
  ClockCircleOutlined,
  LockOutlined,
  UnlockOutlined,
  AppstoreOutlined,
  BarsOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../store';
import {
  fetchProjects,
  createProject,
  updateProject,
  deleteProject,
  createScene,
  fetchProjectScenes,
  setCurrentProject,
  setCurrentScene,
  Project} from '../store/project/projectSlice';

const { Title } = Typography;
const { Option } = Select;
const { Search } = Input;

// 定义创建新项目卡片的类型
interface CreateProjectCard {
  id: string;
  isCreateCard: true;
}

// 定义项目列表项的联合类型
type ProjectListItem = Project | CreateProjectCard;

export const ProjectsPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  const { projects, isLoading, error } = useAppSelector((state) => state.project);
  
  const [searchValue, setSearchValue] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [newProjectModalVisible, setNewProjectModalVisible] = useState(false);
  const [newSceneModalVisible, setNewSceneModalVisible] = useState(false);
  const [deleteProjectModalVisible, setDeleteProjectModalVisible] = useState(false);
  const [editProjectModalVisible, setEditProjectModalVisible] = useState(false);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  
  const [form] = Form.useForm();
  const [sceneForm] = Form.useForm();
  const [editForm] = Form.useForm();
  
  // 检查认证状态
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: '/projects' } });
    }
  }, [isAuthenticated, navigate]);
  
  // 加载项目列表
  useEffect(() => {
    if (isAuthenticated) {
      // 添加延迟，确保认证状态稳定后再加载项目
      const timer = setTimeout(() => {
        dispatch(fetchProjects()).catch((error) => {
          // 静默处理错误，避免在控制台显示过多错误信息
          console.warn('加载项目列表失败:', error);
        });
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [dispatch, isAuthenticated]);
  
  // 处理错误
  useEffect(() => {
    if (error) {
      message.error(error);
    }
  }, [error]);
  
  // 过滤项目
  const filteredProjects = projects.filter(
    (project) =>
      project.name.toLowerCase().includes(searchValue.toLowerCase()) ||
      project.description.toLowerCase().includes(searchValue.toLowerCase())
  );
  
  // 创建新项目
  const handleCreateProject = () => {
    form.validateFields().then((values) => {
      dispatch(createProject(values))
        .unwrap()
        .then((newProject) => {
          setNewProjectModalVisible(false);
          form.resetFields();
          message.success(t('projects.createSuccess'));

          // 创建项目成功后，保持在项目列表页面，不跳转
          // 这样用户可以继续创建更多项目
          console.log('项目创建成功:', newProject);

          // Redux状态已经在createProject.fulfilled中自动更新了项目列表
          // 不需要再次调用fetchProjects()
        })
        .catch((error) => {
          console.error('创建项目失败:', error);
          message.error(error || t('projects.createError'));
        });
    }).catch((validationError) => {
      console.error('表单验证失败:', validationError);
    });
  };
  
  // 创建新场景
  const handleCreateScene = () => {
    if (!selectedProject) {
      console.error('创建场景失败: 未选择项目');
      return;
    }

    sceneForm.validateFields().then((values) => {
      dispatch(createScene({ projectId: selectedProject.id, ...values }))
        .unwrap()
        .then((scene) => {
          setNewSceneModalVisible(false);
          sceneForm.resetFields();
          message.success(t('projects.sceneCreateSuccess'));

          // 导航到编辑器
          dispatch(setCurrentProject(selectedProject));
          dispatch(setCurrentScene(scene));
          navigate(`/editor/${selectedProject.id}/${scene.id}`);
        })
        .catch((error) => {
          console.error('创建场景失败:', error);
          message.error(error || t('projects.sceneCreateError'));
        });
    }).catch((validationError) => {
      console.error('场景表单验证失败:', validationError);
    });
  };
  
  // 编辑项目
  const handleEditProject = () => {
    if (!selectedProject) {
      console.error('编辑项目失败: 未选择项目');
      return;
    }

    editForm.validateFields().then((values) => {
      dispatch(updateProject({ projectId: selectedProject.id, data: values }))
        .unwrap()
        .then(() => {
          setEditProjectModalVisible(false);
          setSelectedProject(null);
          editForm.resetFields();
          message.success(t('projects.updateSuccess'));
        })
        .catch((error) => {
          console.error('编辑项目失败:', error);
          message.error(error || t('projects.updateError'));
        });
    }).catch((validationError) => {
      console.error('编辑项目表单验证失败:', validationError);
    });
  };

  // 删除项目
  const handleDeleteProject = async () => {
    if (!selectedProject) {
      console.error('删除项目失败: 未选择项目');
      message.error('请先选择要删除的项目');
      return;
    }

    try {
      console.log('开始删除项目:', selectedProject.name, selectedProject.id);

      await dispatch(deleteProject(selectedProject.id)).unwrap();

      // 删除成功
      setDeleteProjectModalVisible(false);
      setSelectedProject(null);
      message.success(t('projects.deleteSuccess') || '项目删除成功');

      // 刷新项目列表
      dispatch(fetchProjects());

    } catch (error: any) {
      console.error('删除项目失败:', error);

      // 显示具体的错误信息
      const errorMessage = typeof error === 'string' ? error : (error?.message || t('projects.deleteError') || '删除项目失败');
      message.error(errorMessage);

      // 如果是认证错误，可能需要重新登录
      if (errorMessage.includes('认证') || errorMessage.includes('登录')) {
        // 可以在这里添加跳转到登录页面的逻辑
        console.warn('需要重新登录');
      }
    }
  };
  
  // 打开项目
  const handleOpenProject = async (project: any) => {
    dispatch(setCurrentProject(project));

    try {
      // 首先获取项目的场景列表
      const scenesResult = await dispatch(fetchProjectScenes(project.id)).unwrap();

      // 如果项目有场景，打开第一个场景
      if (scenesResult && scenesResult.length > 0) {
        const scene = scenesResult[0];
        dispatch(setCurrentScene(scene));
        navigate(`/editor/${project.id}/${scene.id}`);
      } else {
        // 否则打开新建场景对话框
        setSelectedProject(project);
        setNewSceneModalVisible(true);
      }
    } catch (error) {
      console.error('获取项目场景失败:', error);
      // 如果获取场景失败，仍然可以创建新场景
      setSelectedProject(project);
      setNewSceneModalVisible(true);
    }
  };
  
  // 渲染网格视图
  const renderGridView = () => {
    // 创建一个包含"创建新项目"卡片的数据源
    const dataSourceWithCreateCard: ProjectListItem[] = [
      { id: 'create-new', isCreateCard: true }, // 添加创建新项目的特殊项目
      ...filteredProjects
    ];

    // 如果没有项目且有搜索条件，显示空状态
    if (filteredProjects.length === 0 && searchValue) {
      return (
        <div>
          <div style={{ marginBottom: 24 }}>
            <Card
              hoverable
              style={{
                border: '2px dashed #1890ff',
                backgroundColor: '#f8f9fa',
                cursor: 'pointer'
              }}
              onClick={() => setNewProjectModalVisible(true)}
              cover={
                <div
                  style={{
                    height: 160,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    color: '#1890ff'
                  }}
                >
                  <PlusOutlined style={{ fontSize: 48, marginBottom: 8 }} />
                  <span style={{ fontSize: 16, fontWeight: 500 }}>{t('projects.new')}</span>
                </div>
              }
            >
              <Card.Meta
                title={t('projects.createNew')}
                description={t('projects.createNewDescription')}
              />
            </Card>
          </div>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t('projects.noSearchResults')}
          />
        </div>
      );
    }

    return (
      <List
        grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 3, xl: 4, xxl: 6 }}
        dataSource={dataSourceWithCreateCard}
        renderItem={(item: ProjectListItem) => {
          // 如果是创建新项目卡片
          if ('isCreateCard' in item && item.isCreateCard) {
            return (
              <List.Item>
                <Card
                  hoverable
                  style={{
                    border: '2px dashed #1890ff',
                    backgroundColor: '#f8f9fa',
                    cursor: 'pointer'
                  }}
                  onClick={() => setNewProjectModalVisible(true)}
                  cover={
                    <div
                      style={{
                        height: 160,
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        color: '#1890ff'
                      }}
                    >
                      <PlusOutlined style={{ fontSize: 48, marginBottom: 8 }} />
                      <span style={{ fontSize: 16, fontWeight: 500 }}>{t('projects.new')}</span>
                    </div>
                  }
                >
                  <Card.Meta
                    title={t('projects.createNew')}
                    description={t('projects.createNewDescription')}
                  />
                </Card>
              </List.Item>
            );
          }

          // 正常的项目卡片
          const project = item as Project;
          return (
            <List.Item>
              <Card
              hoverable
              onClick={() => handleOpenProject(project)}
              style={{ cursor: 'pointer' }}
              cover={
                project.thumbnail ? (
                  <img alt={project.name} src={project.thumbnail} style={{ height: 160, objectFit: 'cover' }} />
                ) : (
                  <div
                    style={{
                      height: 160,
                      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      border: '2px dashed #d9d9d9'
                    }}
                  >
                    <FolderOpenOutlined style={{ fontSize: 48, color: '#1890ff' }} />
                  </div>
                )
              }
              actions={[
                <Tooltip title={t('projects.open')}>
                  <Button
                    type="text"
                    icon={<FolderOpenOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenProject(project);
                    }}
                  />
                </Tooltip>,
                <Tooltip title={t('projects.edit')}>
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedProject(project);
                      editForm.setFieldsValue({
                        name: project.name,
                        description: project.description,
                        isPublic: project.isPublic
                      });
                      setEditProjectModalVisible(true);
                    }}
                  />
                </Tooltip>,
                <Tooltip title={t('projects.delete')}>
                  <Button
                    type="text"
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedProject(project);
                      setDeleteProjectModalVisible(true);
                    }}
                  />
                </Tooltip>,
              ]}
            >
              <Card.Meta
                title={project.name}
                description={
                  <>
                    <div style={{ marginBottom: 8 }}>{project.description}</div>
                    <div>
                      <Space>
                        <Tag icon={<ClockCircleOutlined />}>
                          {new Date(project.updatedAt).toLocaleDateString()}
                        </Tag>
                        <Tag icon={project.isPublic ? <UnlockOutlined /> : <LockOutlined />}>
                          {project.isPublic ? t('projects.public') : t('projects.private')}
                        </Tag>
                      </Space>
                    </div>
                  </>
                }
              />
              </Card>
            </List.Item>
          );
        }}
      />
    );
  };
  
  // 渲染列表视图
  const renderListView = () => {
    // 创建一个包含"创建新项目"项的数据源
    const dataSourceWithCreateItem: ProjectListItem[] = [
      { id: 'create-new', isCreateCard: true }, // 添加创建新项目的特殊项目
      ...filteredProjects
    ];

    // 如果没有项目且有搜索条件，显示空状态
    if (filteredProjects.length === 0 && searchValue) {
      return (
        <div>
          <List.Item
            style={{
              border: '2px dashed #1890ff',
              borderRadius: 8,
              marginBottom: 16,
              backgroundColor: '#f8f9fa',
              cursor: 'pointer'
            }}
            onClick={() => setNewProjectModalVisible(true)}
            actions={[
              <Button type="primary" icon={<PlusOutlined />}>
                {t('projects.new')}
              </Button>
            ]}
          >
            <List.Item.Meta
              avatar={
                <div
                  style={{
                    width: 48,
                    height: 48,
                    background: '#1890ff',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: 4,
                    color: 'white'
                  }}
                >
                  <PlusOutlined style={{ fontSize: 24 }} />
                </div>
              }
              title={<span style={{ color: '#1890ff', fontWeight: 500 }}>{t('projects.createNew')}</span>}
              description={t('projects.createNewDescription')}
            />
          </List.Item>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={t('projects.noSearchResults')}
          />
        </div>
      );
    }

    return (
      <List
        itemLayout="horizontal"
        dataSource={dataSourceWithCreateItem}
        renderItem={(item: ProjectListItem) => {
          // 如果是创建新项目项
          if ('isCreateCard' in item && item.isCreateCard) {
            return (
              <List.Item
                style={{
                  border: '2px dashed #1890ff',
                  borderRadius: 8,
                  marginBottom: 16,
                  backgroundColor: '#f8f9fa',
                  cursor: 'pointer'
                }}
                onClick={() => setNewProjectModalVisible(true)}
                actions={[
                  <Button type="primary" icon={<PlusOutlined />}>
                    {t('projects.new')}
                  </Button>
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <div
                      style={{
                        width: 48,
                        height: 48,
                        background: '#1890ff',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderRadius: 4,
                        color: 'white'
                      }}
                    >
                      <PlusOutlined style={{ fontSize: 24 }} />
                    </div>
                  }
                  title={<span style={{ color: '#1890ff', fontWeight: 500 }}>{t('projects.createNew')}</span>}
                  description={t('projects.createNewDescription')}
                />
              </List.Item>
            );
          }

          // 正常的项目列表项
          const project = item as Project;
          return (
            <List.Item
            onClick={() => handleOpenProject(project)}
            style={{ cursor: 'pointer' }}
            actions={[
              <Button
                type="link"
                onClick={(e) => {
                  e.stopPropagation();
                  handleOpenProject(project);
                }}
              >
                {t('projects.open')}
              </Button>,
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedProject(project);
                  editForm.setFieldsValue({
                    name: project.name,
                    description: project.description,
                    isPublic: project.isPublic
                  });
                  setEditProjectModalVisible(true);
                }}
              />,
              <Button
                type="text"
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedProject(project);
                  setDeleteProjectModalVisible(true);
                }}
              />,
            ]}
          >
            <List.Item.Meta
              avatar={
                project.thumbnail ? (
                  <img
                    src={project.thumbnail}
                    alt={project.name}
                    style={{ width: 48, height: 48, objectFit: 'cover', borderRadius: 4 }}
                  />
                ) : (
                  <div
                    style={{
                      width: 48,
                      height: 48,
                      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: 4,
                      border: '1px solid #d9d9d9'
                    }}
                  >
                    <FolderOpenOutlined style={{ fontSize: 24, color: '#1890ff' }} />
                  </div>
                )
              }
              title={project.name}
              description={
                <>
                  <div>{project.description}</div>
                  <div style={{ marginTop: 4 }}>
                    <Space>
                      <Tag icon={<ClockCircleOutlined />}>
                        {new Date(project.updatedAt).toLocaleDateString()}
                      </Tag>
                      <Tag icon={project.isPublic ? <UnlockOutlined /> : <LockOutlined />}>
                        {project.isPublic ? t('projects.public') : t('projects.private')}
                      </Tag>
                    </Space>
                  </div>
                </>
              }
            />
          </List.Item>
          );
        }}
      />
    );
  };
  
  return (
    <div style={{ padding: 24 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 24 }}>
        <Title level={2}>{t('projects.title')}</Title>
        <Space>
          <Search
            placeholder={t('projects.search') as string}
            allowClear
            onChange={(e) => setSearchValue(e.target.value)}
            style={{ width: 250 }}
          />
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setNewProjectModalVisible(true)}>
            {t('projects.new')}
          </Button>
          <Tooltip title={t('projects.gridView')}>
            <Button
              type={viewMode === 'grid' ? 'primary' : 'default'}
              icon={<AppstoreOutlined />}
              onClick={() => setViewMode('grid')}
            />
          </Tooltip>
          <Tooltip title={t('projects.listView')}>
            <Button
              type={viewMode === 'list' ? 'primary' : 'default'}
              icon={<BarsOutlined />}
              onClick={() => setViewMode('list')}
            />
          </Tooltip>
        </Space>
      </div>
      
      <Tabs
        defaultActiveKey="all"
        items={[
          {
            key: 'all',
            label: t('projects.allProjects'),
            children: isLoading ? (
              <div style={{ display: 'flex', justifyContent: 'center', padding: 40 }}>
                <Spin size="large" />
              </div>
            ) : (
              // 始终显示项目列表（包括创建新项目的卡片/项）
              viewMode === 'grid' ? renderGridView() : renderListView()
            )
          },
          {
            key: 'my',
            label: t('projects.myProjects'),
            children: <Empty description={t('projects.noMyProjects')} />
          },
          {
            key: 'shared',
            label: t('projects.shared'),
            children: <Empty description={t('projects.noSharedProjects')} />
          }
        ]}
      />
      
      {/* 新建项目对话框 */}
      <Modal
        title={t('projects.newProject')}
        open={newProjectModalVisible}
        onOk={handleCreateProject}
        onCancel={() => {
          try {
            setNewProjectModalVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        okText={t('common.create')}
        cancelText={t('common.cancel')}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label={t('projects.name')}
            rules={[{ required: true, message: t('projects.nameRequired') as string }]}
          >
            <Input placeholder={t('projects.namePlaceholder') as string} />
          </Form.Item>
          <Form.Item name="description" label={t('projects.description')}>
            <Input.TextArea rows={4} placeholder={t('projects.descriptionPlaceholder') as string} />
          </Form.Item>
          <Form.Item name="isPublic" label={t('projects.visibility')} initialValue={false}>
            <Select>
              <Option value={false}>{t('projects.private')}</Option>
              <Option value={true}>{t('projects.public')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 新建场景对话框 */}
      <Modal
        title={t('projects.newScene')}
        open={newSceneModalVisible}
        onOk={handleCreateScene}
        onCancel={() => {
          try {
            setNewSceneModalVisible(false)
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        okText={t('common.create')}
        cancelText={t('common.cancel')}
      >
        <Form form={sceneForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('projects.sceneName')}
            rules={[{ required: true, message: t('projects.sceneNameRequired') as string }]}
          >
            <Input placeholder={t('projects.sceneNamePlaceholder') as string} />
          </Form.Item>
          <Form.Item name="description" label={t('projects.sceneDescription')}>
            <Input.TextArea rows={4} placeholder={t('projects.sceneDescriptionPlaceholder') as string} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑项目对话框 */}
      <Modal
        title={t('projects.editProject')}
        open={editProjectModalVisible}
        onOk={handleEditProject}
        onCancel={() => {
          try {
            setEditProjectModalVisible(false);
            setSelectedProject(null);
            editForm.resetFields();
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        okText={t('common.update')}
        cancelText={t('common.cancel')}
        destroyOnClose
        keyboard={true}
        maskClosable={true}
      >
        <Form form={editForm} layout="vertical">
          <Form.Item
            name="name"
            label={t('projects.name')}
            rules={[{ required: true, message: t('projects.nameRequired') as string }]}
          >
            <Input placeholder={t('projects.namePlaceholder') as string} />
          </Form.Item>
          <Form.Item name="description" label={t('projects.description')}>
            <Input.TextArea rows={4} placeholder={t('projects.descriptionPlaceholder') as string} />
          </Form.Item>
          <Form.Item name="isPublic" label={t('projects.visibility')}>
            <Select>
              <Option value={false}>{t('projects.private')}</Option>
              <Option value={true}>{t('projects.public')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 删除项目确认对话框 */}
      <Modal
        title={t('projects.deleteConfirmTitle') || '确认删除项目'}
        open={deleteProjectModalVisible}
        onOk={handleDeleteProject}
        onCancel={() => {
          try {
            setDeleteProjectModalVisible(false);
            setSelectedProject(null);
          } catch (error) {
            console.error('Modal关闭失败:', error);
          }
        }}
        okText={t('common.delete') || '删除'}
        cancelText={t('common.cancel') || '取消'}
        okButtonProps={{
          danger: true,
          loading: isLoading // 添加加载状态
        }}
        confirmLoading={isLoading}
        maskClosable={false}
        destroyOnClose={true}
      >
        <div style={{ marginBottom: 16 }}>
          <p style={{ marginBottom: 8, fontSize: 14 }}>
            {t('projects.deleteConfirmMessage', {
              name: selectedProject?.name || '未知项目'
            }) || `确定要删除项目 "${selectedProject?.name || '未知项目'}" 吗？`}
          </p>
          <p style={{ marginBottom: 0, fontSize: 12, color: '#ff4d4f' }}>
            {t('projects.deleteConfirmWarning') || '此操作不可撤销，项目中的所有数据将被永久删除！'}
          </p>
        </div>
      </Modal>
    </div>
  );
};
